#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的2FA流程
"""

import requests
import json
import urllib.parse
from totp_utils import generate_totp_code

def test_full_2fa_flow():
    """测试完整的2FA流程"""
    print("🧪 测试完整2FA流程...")
    
    # 使用测试账户的session
    test_session = "ca5lmqdqvydpuion4scpkywws4rtwfctx5pf3ebw"
    
    session = requests.Session()
    
    # 设置请求头
    headers = {
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'content-type': 'application/json',
        'referer': 'https://targon.com/two-factor-auth',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
        'x-trpc-source': 'react'
    }
    
    session.headers.update(headers)
    session.cookies.update({'auth_session': test_session})
    
    print("🔐 步骤1: 获取2FA URI...")
    
    # 构建批量请求数据
    batch_data = {
        "0": {
            "json": None,
            "meta": {
                "values": ["undefined"]
            }
        },
        "1": {
            "json": None,
            "meta": {
                "values": ["undefined"]
            }
        },
        "2": {
            "json": None,
            "meta": {
                "values": ["undefined"]
            }
        }
    }
    
    # 构建URL
    input_param = urllib.parse.quote(json.dumps(batch_data))
    url = f'https://targon.com/api/trpc/model.getAll,account.createTwoFactorURI,account.getUser?batch=1&input={input_param}'
    
    try:
        response = session.get(url, timeout=30)
        print(f"📡 2FA URI响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            # 查找2FA数据
            two_factor_secret = None
            manual_code = None
            
            for i, item in enumerate(result):
                if 'result' in item and 'data' in item['result']:
                    data = item['result']['data']['json']
                    if isinstance(data, dict) and 'twoFactorSecret' in data:
                        print(f"✅ 在索引 {i} 找到2FA数据")
                        two_factor_secret = data.get('twoFactorSecret')
                        manual_code = data.get('manualCode')
                        print(f"✅ 2FA Secret: {two_factor_secret}")
                        print(f"✅ Manual Code: {manual_code}")
                        break
            
            if not two_factor_secret or not manual_code:
                print("❌ 未找到2FA数据")
                return
            
            print("🔐 步骤2: 生成TOTP验证码...")
            
            # 生成TOTP验证码
            otp_code = generate_totp_code(manual_code)
            print(f"✅ 生成的OTP验证码: {otp_code}")
            
            print("🔐 步骤3: 启用2FA...")
            
            # 启用2FA
            enable_data = {
                "0": {
                    "json": {
                        "otp": otp_code,
                        "twoFactorSecret": two_factor_secret
                    }
                }
            }
            
            enable_response = session.post(
                'https://targon.com/api/trpc/account.enable2FA?batch=1',
                json=enable_data,
                timeout=30
            )
            
            print(f"📡 启用2FA响应状态: {enable_response.status_code}")
            print(f"📋 启用2FA响应: {enable_response.text}")
            
            if enable_response.status_code == 200:
                print("🎉 2FA启用成功！")
                return True
            else:
                print("❌ 2FA启用失败")
                return False
                
        else:
            print(f"❌ 获取2FA URI失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 2FA流程异常: {e}")
        return False

if __name__ == "__main__":
    test_full_2fa_flow()
