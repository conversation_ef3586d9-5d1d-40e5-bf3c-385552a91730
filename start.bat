@echo off
chcp 65001 >nul
title Targon Register Tool

echo ========================================
echo    Targon Register Tool
echo ========================================
echo.
echo Select Mode:
echo 1 - Interactive Mode
echo 2 - Quick Mode (20 accounts)
echo 3 - Fast Mode (50 accounts)
echo 4 - Crazy Mode (100 accounts)
echo.

set /p choice=Choose mode (1-4): 

if %choice%==1 goto interactive
if %choice%==2 goto quick
if %choice%==3 goto fast
if %choice%==4 goto crazy
goto interactive

:interactive
echo.
echo Starting Interactive Mode...
python targon_register.py
goto end

:quick
echo.
echo Starting Quick Mode - 20 accounts, 80 threads...
python quick_start.py
goto end

:fast
echo.
echo Starting Fast Mode - 50 accounts, 100 threads...
python -c "from targon_register import TargonRegister; TargonRegister(100).run(50)"
goto end

:crazy
echo.
echo Starting Crazy Mode - 100 accounts, 200 threads...
python -c "from targon_register import TargonRegister; TargonRegister(200).run(100)"
goto end

:end
echo.
echo ========================================
echo Registration completed! Check files:
echo - targon_accounts.txt
echo - targon_apikeys.txt
echo ========================================
pause
