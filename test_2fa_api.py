#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试2FA API调用
"""

import requests
import json
import urllib.parse

def test_2fa_api():
    """测试2FA API调用"""
    print("🧪 测试2FA API调用...")
    
    # 使用一个已知的session（从测试账户获取）
    test_session = "ca5lmqdqvydpuion4scpkywws4rtwfctx5pf3ebw"  # 从测试账户获取的有效session
    
    session = requests.Session()
    
    # 设置请求头
    headers = {
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'content-type': 'application/json',
        'referer': 'https://targon.com/two-factor-auth',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
        'x-trpc-source': 'react'
    }
    
    session.headers.update(headers)
    session.cookies.update({'auth_session': test_session})
    
    # 构建批量请求数据
    batch_data = {
        "0": {
            "json": None,
            "meta": {
                "values": ["undefined"]
            }
        },
        "1": {
            "json": None,
            "meta": {
                "values": ["undefined"]
            }
        },
        "2": {
            "json": None,
            "meta": {
                "values": ["undefined"]
            }
        }
    }
    
    # 构建URL
    input_param = urllib.parse.quote(json.dumps(batch_data))
    url = f'https://targon.com/api/trpc/model.getAll,account.createTwoFactorURI,account.getUser?batch=1&input={input_param}'
    
    print(f"🔗 请求URL: {url}")
    print(f"🍪 使用Session: {test_session}")
    
    try:
        response = session.get(url, timeout=30)
        print(f"📡 响应状态: {response.status_code}")
        print(f"📋 响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ JSON解析成功")
            print(f"📊 响应结构: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ API调用失败")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_2fa_api()
