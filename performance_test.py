#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试脚本 - 测试2FA注册机的效率
"""

import time
from targon_register import TargonRegister

def performance_test():
    """性能测试"""
    print("🚀 Targon 2FA注册机性能测试")
    print("=" * 60)
    
    # 测试不同并发数的性能
    test_configs = [
        {"accounts": 5, "workers": 5, "name": "小规模测试"},
        {"accounts": 10, "workers": 20, "name": "中等规模测试"},
        {"accounts": 20, "workers": 50, "name": "大规模测试"}
    ]
    
    results = []
    
    for config in test_configs:
        print(f"\n🧪 {config['name']}: {config['accounts']}个账户, {config['workers']}个并发")
        print("-" * 50)
        
        start_time = time.time()
        
        # 创建注册机实例
        register = TargonRegister(max_workers=config['workers'])
        
        # 执行注册
        register.batch_register(config['accounts'])
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # 计算性能指标
        success_rate = (register.success_count / config['accounts']) * 100
        speed = register.success_count / elapsed if elapsed > 0 else 0
        
        result = {
            "name": config['name'],
            "accounts": config['accounts'],
            "workers": config['workers'],
            "success": register.success_count,
            "failed": register.failed_count,
            "success_rate": success_rate,
            "time": elapsed,
            "speed": speed
        }
        
        results.append(result)
        
        print(f"✅ 成功: {register.success_count}/{config['accounts']}")
        print(f"📊 成功率: {success_rate:.1f}%")
        print(f"⏱️ 耗时: {elapsed:.1f}秒")
        print(f"🚀 速度: {speed:.2f}个/秒")
    
    # 输出总结
    print("\n" + "=" * 60)
    print("📊 性能测试总结")
    print("=" * 60)
    
    for result in results:
        print(f"{result['name']:15} | "
              f"成功率: {result['success_rate']:5.1f}% | "
              f"速度: {result['speed']:5.2f}个/秒 | "
              f"耗时: {result['time']:5.1f}秒")
    
    # 计算平均性能
    avg_success_rate = sum(r['success_rate'] for r in results) / len(results)
    avg_speed = sum(r['speed'] for r in results) / len(results)
    
    print("-" * 60)
    print(f"平均成功率: {avg_success_rate:.1f}%")
    print(f"平均速度: {avg_speed:.2f}个/秒")
    
    # 效率评估
    if avg_success_rate >= 95 and avg_speed >= 1.0:
        print("🎉 性能评估: 优秀 - 高效率，高成功率")
    elif avg_success_rate >= 90 and avg_speed >= 0.8:
        print("✅ 性能评估: 良好 - 效率和成功率都不错")
    elif avg_success_rate >= 80:
        print("⚠️ 性能评估: 一般 - 成功率可接受，但可能需要优化")
    else:
        print("❌ 性能评估: 需要改进 - 成功率或效率偏低")
    
    print("\n🔐 2FA功能: ✅ 已启用并正常工作")
    print("💾 数据保存: ✅ 实时保存，避免数据丢失")
    print("🚀 并发处理: ✅ 多线程高效处理")

if __name__ == "__main__":
    performance_test()
