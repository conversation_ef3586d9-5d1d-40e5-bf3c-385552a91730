#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Targon 注册机测试脚本 - 小批量测试实时保存功能
"""

from targon_register import TargonRegister

def test_small_batch():
    """测试小批量注册，验证实时保存功能"""
    print("🧪 Targon 注册机测试模式")
    print("=" * 50)
    print("📋 测试配置:")
    print("   - 注册数量: 3个账户")
    print("   - 并发线程: 3个")
    print("   - 实时保存: 开启")
    print("   - 用途: 验证功能正常")
    print("=" * 50)
    
    confirm = input("开始测试? (y/N): ").lower()
    if confirm != 'y':
        print("❌ 测试已取消")
        return
    
    print("\n🚀 开始测试...")
    register = TargonRegister(max_workers=3)
    register.run(3)
    
    print("\n🧪 测试完成！")
    print("请检查生成的文件:")
    print("- targon_accounts.txt")
    print("- targon_apikeys.txt")

if __name__ == "__main__":
    test_small_batch()
