#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Targon 注册机快速启动脚本
"""

from targon_register import TargonRegister

def quick_register(count=10, workers=50):
    """快速注册指定数量的账户"""
    print(f"🚀 快速注册 {count} 个账户，并发数: {workers}")

    # 显示现有文件信息
    import os
    if os.path.exists('targon_accounts.txt'):
        with open('targon_accounts.txt', 'r', encoding='utf-8') as f:
            existing = len([line for line in f if line.strip()])
        print(f"📄 现有数据: {existing} 条，将追加 {count} 条新数据")

    register = TargonRegister(max_workers=workers)
    register.run(count)

if __name__ == "__main__":
    # 快速配置 - 可以直接修改这里的参数
    ACCOUNT_COUNT = 20    # 注册数量
    THREAD_COUNT = 80     # 并发线程数

    print("🎯 Targon 快速注册模式")
    print(f"📋 将注册 {ACCOUNT_COUNT} 个账户，使用 {THREAD_COUNT} 个并发线程")
    print("⚡ 高速模式：不在乎服务器死活，追求最大速度！")
    print("💾 实时保存：每个成功账户立即写入文件，避免数据丢失！")
    print("=" * 60)

    quick_register(ACCOUNT_COUNT, THREAD_COUNT)
