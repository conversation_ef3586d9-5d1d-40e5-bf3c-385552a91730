#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TOTP (Time-based One-Time Password) 工具模块
高效实现Google Authenticator兼容的验证码生成
"""

import hmac
import hashlib
import struct
import time
import base64


def base32_decode(secret):
    """
    解码base32格式的secret
    :param secret: base32编码的密钥
    :return: 解码后的字节串
    """
    # 移除空格和转换为大写
    secret = secret.replace(' ', '').upper()
    
    # 添加必要的填充
    missing_padding = len(secret) % 8
    if missing_padding:
        secret += '=' * (8 - missing_padding)
    
    return base64.b32decode(secret)


def generate_totp_code(secret, time_step=30, digits=6, timestamp=None):
    """
    生成TOTP验证码
    :param secret: base32编码的密钥字符串
    :param time_step: 时间步长（秒），默认30秒
    :param digits: 验证码位数，默认6位
    :param timestamp: 指定时间戳，默认使用当前时间
    :return: 6位数字验证码字符串
    """
    if timestamp is None:
        timestamp = int(time.time())
    
    # 计算时间计数器
    time_counter = timestamp // time_step
    
    # 解码secret
    try:
        key = base32_decode(secret)
    except Exception:
        raise ValueError("Invalid base32 secret")
    
    # 将时间计数器转换为8字节大端序
    time_bytes = struct.pack('>Q', time_counter)
    
    # 使用HMAC-SHA1计算hash
    hmac_hash = hmac.new(key, time_bytes, hashlib.sha1).digest()
    
    # 动态截取
    offset = hmac_hash[-1] & 0x0f
    truncated = struct.unpack('>I', hmac_hash[offset:offset + 4])[0]
    truncated &= 0x7fffffff
    
    # 生成指定位数的验证码
    code = truncated % (10 ** digits)
    
    return str(code).zfill(digits)


def verify_totp_code(secret, code, time_step=30, window=1):
    """
    验证TOTP验证码
    :param secret: base32编码的密钥
    :param code: 要验证的验证码
    :param time_step: 时间步长
    :param window: 时间窗口（允许前后几个时间步长）
    :return: 验证是否成功
    """
    current_time = int(time.time())
    
    # 在时间窗口内验证
    for i in range(-window, window + 1):
        timestamp = current_time + (i * time_step)
        expected_code = generate_totp_code(secret, time_step, timestamp=timestamp)
        if expected_code == code:
            return True
    
    return False


if __name__ == "__main__":
    # 测试代码
    test_secret = "JBSWY3DPEHPK3PXP"
    code = generate_totp_code(test_secret)
    print(f"Generated TOTP code: {code}")
    
    # 验证测试
    is_valid = verify_totp_code(test_secret, code)
    print(f"Verification result: {is_valid}")
