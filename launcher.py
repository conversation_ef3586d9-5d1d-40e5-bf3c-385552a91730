#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Targon 注册机启动器
"""

import os
import sys
from targon_register import TargonRegister

def show_menu():
    """显示菜单"""
    print("=" * 50)
    print("    🎯 Targon 高性能注册机")
    print("=" * 50)

    # 显示现有文件信息
    import os
    if os.path.exists('targon_accounts.txt'):
        with open('targon_accounts.txt', 'r', encoding='utf-8') as f:
            existing_accounts = len([line for line in f if line.strip()])
        print(f"📄 现有账户: {existing_accounts} 条")

    if os.path.exists('targon_apikeys.txt'):
        with open('targon_apikeys.txt', 'r', encoding='utf-8') as f:
            existing_keys = len([line for line in f if line.strip()])
        print(f"🔑 现有密钥: {existing_keys} 条")

    print("💾 追加模式: 新数据将追加到现有文件，不会覆盖")
    print()
    print("选择运行模式:")
    print("1. 交互模式 - 自定义数量和并发数")
    print("2. 快速模式 - 20个账户，80并发")
    print("3. 极速模式 - 50个账户，100并发")
    print("4. 疯狂模式 - 100个账户，200并发")
    print("5. 自定义模式 - 快速设置")
    print("0. 退出")
    print()

def custom_mode():
    """自定义模式"""
    try:
        print("\n🔧 自定义模式")
        count = int(input("账户数量: "))
        threads = int(input("并发线程数 (无上限): "))

        if count <= 0 or threads <= 0:
            print("❌ 参数必须大于0")
            return

        if threads > 500:
            print(f"⚡ 超高并发模式: {threads} 线程 - 追求极致速度！")

        print(f"\n🚀 开始注册 {count} 个账户，{threads} 个并发线程")
        register = TargonRegister(max_workers=threads)
        register.run(count)
        
    except ValueError:
        print("❌ 请输入有效数字")
    except Exception as e:
        print(f"❌ 错误: {e}")

def main():
    """主函数"""
    while True:
        try:
            show_menu()
            choice = input("请选择 (0-5): ").strip()
            
            if choice == "0":
                print("👋 再见！")
                break
            elif choice == "1":
                print("\n🔧 交互模式")
                os.system("python targon_register.py")
            elif choice == "2":
                print("\n⚡ 快速模式 - 20个账户，80并发")
                register = TargonRegister(max_workers=80)
                register.run(20)
            elif choice == "3":
                print("\n🚀 极速模式 - 50个账户，100并发")
                register = TargonRegister(max_workers=100)
                register.run(50)
            elif choice == "4":
                print("\n💥 疯狂模式 - 100个账户，200并发")
                register = TargonRegister(max_workers=200)
                register.run(100)
            elif choice == "5":
                custom_mode()
            else:
                print("❌ 无效选择，请重新输入")
                continue
                
            print("\n" + "=" * 50)
            print("✅ 任务完成！检查生成的文件:")
            print("📄 targon_accounts.txt - 完整账户信息")
            print("🔑 targon_apikeys.txt - API密钥列表")
            print("=" * 50)
            
            again = input("\n是否继续注册? (y/N): ").lower()
            if again != 'y':
                break
                
        except KeyboardInterrupt:
            print("\n\n❌ 用户中断")
            break
        except Exception as e:
            print(f"\n❌ 程序错误: {e}")
            continue

if __name__ == "__main__":
    main()
