@echo off
chcp 65001 >nul
title Targon Register Tool

echo.
echo ========================================
echo    Targon Register Tool
echo ========================================
echo.
echo Select Mode:
echo 1 - Interactive Mode
echo 2 - Quick Mode (20 accounts, 80 threads)
echo 3 - Fast Mode (50 accounts, 100 threads)
echo 4 - Crazy Mode (100 accounts, 150 threads)
echo.

set /p choice="Choose mode (1-4): "

if "%choice%"=="1" (
    echo.
    echo Interactive Mode
    python targon_register.py
) else if "%choice%"=="2" (
    echo.
    echo Quick Mode - 20 accounts, 80 threads
    python quick_start.py
) else if "%choice%"=="3" (
    echo.
    echo Fast Mode - 50 accounts, 100 threads
    python -c "from targon_register import TargonRegister; TargonRegister(100).run(50)"
) else if "%choice%"=="4" (
    echo.
    echo Crazy Mode - 100 accounts, 150 threads
    python -c "from targon_register import TargonRegister; TargonRegister(150).run(100)"
) else (
    echo.
    echo Invalid choice, running interactive mode
    python targon_register.py
)

echo.
echo ========================================
echo Registration completed! Check files:
echo - targon_accounts.txt (full info)
echo - targon_apikeys.txt (api keys only)
echo ========================================
pause
