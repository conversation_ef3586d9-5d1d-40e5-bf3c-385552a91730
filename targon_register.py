#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Targon 高性能多线程注册机
支持自定义数量并发注册，快速获取账户和API密钥
"""

import requests
import threading
import time
import random
import string
import re
import json
import urllib.parse
from concurrent.futures import ThreadPoolExecutor, as_completed
from email_utils import create_test_email, fetch_first_email, extract_verification_token
from totp_utils import generate_totp_code
import queue
import sys

class TargonRegister:
    def __init__(self, max_workers=50):
        """
        初始化注册机
        :param max_workers: 最大并发线程数
        """
        self.max_workers = max_workers
        self.session_pool = queue.Queue()
        self.success_count = 0
        self.failed_count = 0
        self.lock = threading.Lock()

        # 文件锁，用于安全写入
        self.file_lock = threading.Lock()

        # 初始化输出文件
        self._init_output_files()
        
        # 请求头模板
        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'content-type': 'application/json',
            'origin': 'https://targon.com',
            'priority': 'u=1, i',
            'referer': 'https://targon.com/sign-in?mode=signup',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
            'x-trpc-source': 'react'
        }
        
        # 初始化会话池
        self._init_session_pool()

    def _init_output_files(self):
        """初始化输出文件"""
        try:
            # 检查文件是否存在，如果存在则追加，不存在则创建
            import os

            if os.path.exists('targon_accounts.txt'):
                with open('targon_accounts.txt', 'r', encoding='utf-8') as f:
                    existing_accounts = len([line for line in f if line.strip()])
                print(f"📄 发现已有账户文件，包含 {existing_accounts} 条记录，将追加新数据")
            else:
                with open('targon_accounts.txt', 'w', encoding='utf-8') as f:
                    f.write("")
                print("📄 创建新的账户文件")

            if os.path.exists('targon_apikeys.txt'):
                with open('targon_apikeys.txt', 'r', encoding='utf-8') as f:
                    existing_keys = len([line for line in f if line.strip()])
                print(f"🔑 发现已有密钥文件，包含 {existing_keys} 条记录，将追加新数据")
            else:
                with open('targon_apikeys.txt', 'w', encoding='utf-8') as f:
                    f.write("")
                print("🔑 创建新的密钥文件")

            print("✅ 输出文件已准备就绪（追加模式）")
        except Exception as e:
            print(f"❌ 初始化输出文件失败: {e}")

    def _init_session_pool(self):
        """初始化会话池"""
        for _ in range(self.max_workers * 2):  # 创建足够的会话
            session = requests.Session()
            session.headers.update(self.headers)
            self.session_pool.put(session)
    
    def get_session(self):
        """获取会话"""
        try:
            return self.session_pool.get_nowait()
        except queue.Empty:
            session = requests.Session()
            session.headers.update(self.headers)
            return session
    
    def return_session(self, session):
        """归还会话"""
        try:
            self.session_pool.put_nowait(session)
        except queue.Full:
            pass
    
    def generate_password(self):
        """生成随机密码"""
        # 使用复杂密码格式：!Qwedcxzas1232
        chars = string.ascii_letters + string.digits
        password = "!" + ''.join(random.choices(string.ascii_uppercase, k=1)) + \
                  ''.join(random.choices(chars, k=10)) + \
                  ''.join(random.choices(string.digits, k=4))
        return password
    
    def register_account(self, email, password):
        """注册账户"""
        session = self.get_session()
        try:
            # 设置初始cookie
            session.cookies.update({
                'google_redirect': '%2F',
                'google_oauth_state': 'pUNKdXh0vh5gBaTT4WFgENdYV-jIxKEmL8aGHfQ8dTk',
                'google_code_verifier': 'qpD_UJO1e5vHbeMOQrPB3vFxnrZmUT9sdovQYjG8Ims'
            })
            
            # 注册请求
            register_data = {
                "0": {
                    "json": {
                        "email": email,
                        "password": password,
                        "password2": password
                    }
                }
            }
            
            response = session.post(
                'https://targon.com/api/trpc/account.createAccount?batch=1',
                json=register_data,
                timeout=30
            )
            
            if response.status_code == 200:
                print(f"✅ 注册成功: {email}")
                return session, True
            else:
                print(f"❌ 注册失败: {email} - {response.status_code}")
                return session, False
                
        except Exception as e:
            print(f"❌ 注册异常: {email} - {e}")
            return session, False
        finally:
            self.return_session(session)
    
    def verify_email(self, email_id, email_address):
        """验证邮箱并获取验证链接"""
        max_attempts = 30  # 最多等待5分钟
        for attempt in range(max_attempts):
            try:
                email_content = fetch_first_email(email_id)
                if email_content:
                    # 提取验证token
                    token = extract_verification_token(email_content)
                    if token:
                        print(f"✅ 获取验证token: {email_address} - {token[:20]}...")
                        return token
                
                if attempt < max_attempts - 1:
                    time.sleep(10)  # 等待10秒
                    
            except Exception as e:
                print(f"❌ 获取邮件异常: {email_address} - {e}")
                
        print(f"❌ 未收到验证邮件: {email_address}")
        return None
    
    def activate_account(self, token):
        """激活账户"""
        session = self.get_session()
        try:
            # 访问验证链接
            verify_url = f"https://targon.com/email-verification/?token={token}"
            response = session.get(verify_url, timeout=30, allow_redirects=True)
            
            if response.status_code == 200:
                # 检查是否有auth_session cookie
                auth_session = None
                for cookie in session.cookies:
                    if cookie.name == 'auth_session':
                        auth_session = cookie.value
                        break
                
                if auth_session:
                    print(f"✅ 账户激活成功，获取到session: {auth_session[:20]}...")
                    return session, auth_session
                else:
                    print(f"❌ 激活成功但未获取到session")
                    return session, None
            else:
                print(f"❌ 激活失败: {response.status_code}")
                return session, None
                
        except Exception as e:
            print(f"❌ 激活异常: {e}")
            return session, None
        finally:
            self.return_session(session)
    
    def create_two_factor_uri(self, session, auth_session):
        """创建2FA URI并获取secret"""
        try:
            # 更新session的cookie
            session.cookies.update({'auth_session': auth_session})

            # 更新请求头，添加必要的头部
            session.headers.update({
                'referer': 'https://targon.com/two-factor-auth',
                'x-trpc-source': 'react'
            })

            # 使用批量请求格式，包含多个API调用
            batch_data = {
                "0": {
                    "json": None,
                    "meta": {
                        "values": ["undefined"]
                    }
                },
                "1": {
                    "json": None,
                    "meta": {
                        "values": ["undefined"]
                    }
                },
                "2": {
                    "json": None,
                    "meta": {
                        "values": ["undefined"]
                    }
                }
            }

            # 构建URL参数
            input_param = urllib.parse.quote(json.dumps(batch_data))
            url = f'https://targon.com/api/trpc/model.getAll,account.createTwoFactorURI,account.getUser?batch=1&input={input_param}'

            print(f"🔗 请求2FA URI: {url[:100]}...")

            # 使用GET请求
            response = session.get(url, timeout=30)

            print(f"📡 2FA URI响应状态: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                print(f"📋 2FA URI响应长度: {len(result)}")

                if result and len(result) >= 2 and 'result' in result[1]:
                    # createTwoFactorURI 是第2个请求 (索引1)
                    data = result[1]['result']['data']['json']
                    two_factor_secret = data.get('twoFactorSecret')
                    manual_code = data.get('manualCode')
                    print(f"✅ 获取2FA secret成功: {manual_code}")
                    return two_factor_secret, manual_code
                else:
                    print(f"❌ 2FA URI响应格式错误，尝试查看完整响应")
                    for i, item in enumerate(result):
                        if 'result' in item and 'data' in item['result']:
                            data = item['result']['data']['json']
                            if isinstance(data, dict) and 'twoFactorSecret' in data:
                                print(f"✅ 在索引 {i} 找到2FA数据")
                                two_factor_secret = data.get('twoFactorSecret')
                                manual_code = data.get('manualCode')
                                print(f"✅ 获取2FA secret成功: {manual_code}")
                                return two_factor_secret, manual_code
                    print(f"❌ 未找到2FA数据")
                    return None, None
            else:
                print(f"❌ 获取2FA URI失败: {response.status_code}")
                print(f"❌ 响应内容: {response.text}")
                return None, None

        except Exception as e:
            print(f"❌ 获取2FA URI异常: {e}")
            return None, None

    def enable_two_factor(self, session, auth_session, two_factor_secret, manual_code):
        """启用2FA验证"""
        try:
            # 更新session的cookie
            session.cookies.update({'auth_session': auth_session})

            # 生成TOTP验证码
            otp_code = generate_totp_code(manual_code)
            print(f"✅ 生成TOTP验证码: {otp_code}")

            # 启用2FA
            enable_data = {
                "0": {
                    "json": {
                        "otp": otp_code,
                        "twoFactorSecret": two_factor_secret
                    }
                }
            }

            response = session.post(
                'https://targon.com/api/trpc/account.enable2FA?batch=1',
                json=enable_data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                print(f"✅ 2FA启用成功: {result}")
                return True
            else:
                print(f"❌ 启用2FA失败: {response.status_code}")
                print(f"❌ 响应内容: {response.text}")
                return False

        except Exception as e:
            print(f"❌ 启用2FA异常: {e}")
            return False

    def get_api_key(self, session, auth_session):
        """获取API密钥"""
        try:
            # 更新session的cookie
            session.cookies.update({'auth_session': auth_session})

            # 请求API密钥
            api_key_data = {
                "0": {
                    "json": {
                        "name": f"Key_{random.randint(1000, 9999)}"
                    }
                }
            }

            response = session.post(
                'https://targon.com/api/trpc/keys.createApiKey?batch=1',
                json=api_key_data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if result and len(result) > 0 and 'result' in result[0]:
                    api_key = result[0]['result']['data']['json']
                    print(f"✅ 获取API密钥成功: {api_key}")
                    return api_key
                else:
                    print(f"❌ API密钥响应格式错误: {result}")
                    return None
            else:
                print(f"❌ 获取API密钥失败: {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ 获取API密钥异常: {e}")
            return None
    
    def process_single_account(self, index):
        """处理单个账户的完整流程"""
        try:
            print(f"🚀 开始处理账户 #{index}")
            
            # 1. 创建邮箱
            email_id, email_address = create_test_email()
            if not email_id or not email_address:
                print(f"❌ 创建邮箱失败 #{index}")
                with self.lock:
                    self.failed_count += 1
                return
            
            # 2. 生成密码
            password = self.generate_password()
            
            # 3. 注册账户
            session, register_success = self.register_account(email_address, password)
            if not register_success:
                print(f"❌ 注册失败 #{index}")
                with self.lock:
                    self.failed_count += 1
                return
            
            # 4. 验证邮箱
            token = self.verify_email(email_id, email_address)
            if not token:
                print(f"❌ 邮箱验证失败 #{index}")
                with self.lock:
                    self.failed_count += 1
                return
            
            # 5. 激活账户
            session, auth_session = self.activate_account(token)
            if not auth_session:
                print(f"❌ 账户激活失败 #{index}")
                with self.lock:
                    self.failed_count += 1
                return

            # 6. 设置2FA验证
            two_factor_secret, manual_code = self.create_two_factor_uri(session, auth_session)
            if not two_factor_secret or not manual_code:
                print(f"❌ 获取2FA信息失败 #{index}")
                with self.lock:
                    self.failed_count += 1
                return

            # 7. 启用2FA
            if not self.enable_two_factor(session, auth_session, two_factor_secret, manual_code):
                print(f"❌ 启用2FA失败 #{index}")
                with self.lock:
                    self.failed_count += 1
                return

            # 8. 获取API密钥
            api_key = self.get_api_key(session, auth_session)
            if not api_key:
                print(f"❌ 获取API密钥失败 #{index}")
                with self.lock:
                    self.failed_count += 1
                return
            
            # 9. 立即保存结果到文件
            cookie_str = f"auth_session={auth_session}"
            result_line = f"{email_address}----{password}----{cookie_str}----{api_key}----2FA_ENABLED"

            # 立即写入文件，避免数据丢失
            self._write_to_files(result_line, api_key)

            with self.lock:
                self.success_count += 1

            print(f"🎉 账户 #{index} 处理完成并已保存(含2FA): {email_address}")
            
        except Exception as e:
            print(f"❌ 处理账户 #{index} 异常: {e}")
            with self.lock:
                self.failed_count += 1

    def _write_to_files(self, result_line, api_key):
        """线程安全地写入文件"""
        with self.file_lock:
            try:
                # 写入完整信息文件
                with open('targon_accounts.txt', 'a', encoding='utf-8') as f:
                    f.write(result_line + '\n')
                    f.flush()  # 立即刷新到磁盘

                # 写入API密钥文件
                with open('targon_apikeys.txt', 'a', encoding='utf-8') as f:
                    f.write(api_key + '\n')
                    f.flush()  # 立即刷新到磁盘

            except Exception as e:
                print(f"❌ 写入文件失败: {e}")
    
    def run(self, total_count):
        """运行注册机"""
        print(f"🚀 开始批量注册 {total_count} 个账户，并发数: {self.max_workers}")
        print("=" * 60)
        
        start_time = time.time()
        
        # 使用线程池执行
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            futures = [executor.submit(self.process_single_account, i+1) for i in range(total_count)]
            
            # 等待所有任务完成
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    print(f"❌ 任务执行异常: {e}")
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # 输出统计信息
        print("=" * 60)
        print(f"📊 注册完成统计:")
        print(f"   总数量: {total_count}")
        print(f"   成功: {self.success_count}")
        print(f"   失败: {self.failed_count}")
        print(f"   成功率: {self.success_count/total_count*100:.1f}%")
        print(f"   耗时: {elapsed_time:.1f}秒")
        print(f"   平均速度: {self.success_count/elapsed_time:.2f}个/秒")
        
        # 显示最终文件信息
        self.show_final_results()

    def show_final_results(self):
        """显示最终结果文件信息"""
        try:
            # 统计文件中的行数
            with open('targon_accounts.txt', 'r', encoding='utf-8') as f:
                account_lines = len([line for line in f if line.strip()])

            with open('targon_apikeys.txt', 'r', encoding='utf-8') as f:
                apikey_lines = len([line for line in f if line.strip()])

            print(f"✅ 完整账户信息: targon_accounts.txt (总计 {account_lines} 条，本次新增 {self.success_count} 条)")
            print(f"✅ API密钥文件: targon_apikeys.txt (总计 {apikey_lines} 条，本次新增 {self.success_count} 条)")

        except Exception as e:
            print(f"❌ 读取结果文件失败: {e}")
            print("✅ 结果已实时保存到文件中")

def main():
    """主函数"""
    print("🎯 Targon 高性能注册机")
    print("=" * 60)
    
    try:
        # 获取用户输入
        total_count = int(input("请输入要注册的账户数量: "))
        max_workers = int(input(f"请输入并发线程数 (无上限): ") or "50")

        if total_count <= 0:
            print("❌ 账户数量必须大于0")
            return

        if max_workers <= 0:
            print("❌ 并发线程数必须大于0")
            return

        if max_workers > 500:
            print(f"⚡ 超高并发模式: {max_workers} 线程 - 追求极致速度！")
        
        # 确认开始
        print(f"\n📋 配置信息:")
        print(f"   注册数量: {total_count}")
        print(f"   并发线程: {max_workers}")
        print(f"   预计耗时: {total_count/max_workers*30:.0f}秒")
        
        confirm = input("\n确认开始注册? (y/N): ").lower()
        if confirm != 'y':
            print("❌ 已取消注册")
            return
        
        # 开始注册
        register = TargonRegister(max_workers=max_workers)
        register.run(total_count)
        
    except KeyboardInterrupt:
        print("\n❌ 用户中断注册")
    except ValueError:
        print("❌ 输入格式错误，请输入数字")
    except Exception as e:
        print(f"❌ 程序异常: {e}")

if __name__ == "__main__":
    main()
