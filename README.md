# 🎯 Targon 高性能注册机 (含2FA验证)

一个高性能的多线程 Targon 账户注册工具，支持自定义数量并发注册，自动启用2FA安全验证，追求极致速度！
API url:https://api.targon.com/v1/chat/completions

## ✨ 特性

- 🚀 **高并发**: 支持最高200个并发线程
- ⚡ **极速模式**: 不在乎服务器死活，追求最大注册速度
- 🎯 **自动化**: 全流程自动化 - 邮箱创建→注册→验证→激活→2FA启用→获取API密钥
- 🔐 **2FA安全**: 自动启用双因子验证，内置TOTP算法生成验证码
- 📊 **实时统计**: 实时显示成功率、速度等统计信息
- 💾 **实时保存**: 每个成功账户立即写入文件，避免中途出错数据丢失
- 📁 **追加模式**: 新数据追加到现有文件，不会覆盖以前的密钥
- 📊 **数据管理**: 自动统计现有数据，支持查看和管理
- 🔄 **双文件输出**: 自动生成完整信息和API密钥两个文件

## 📁 文件说明

- `targon_register.py` - 主注册程序（交互模式，含2FA）
- `totp_utils.py` - TOTP算法工具模块（2FA验证码生成）
- `test_2fa.py` - 2FA功能测试脚本
- `quick_start.py` - 快速启动脚本（预设参数）
- `test_register.py` - 测试脚本（小批量验证）
- `launcher.py` - Python启动器（推荐）
- `view_data.py` - 数据查看器（查看现有数据）
- `email_utils.py` - 邮箱工具库（已准备好）
- `start.bat` - Windows批处理启动器
- `README.md` - 使用说明

## 🚀 快速开始

### 方法1: Python启动器（推荐）
```bash
# 菜单模式，最稳定
python launcher.py
```

### 方法2: Windows批处理
```bash
# 双击运行
start.bat
```

### 方法3: Python直接运行
```bash
# 查看现有数据
python view_data.py

# 测试模式 - 3个账户验证功能
python test_register.py

# 交互模式 - 自定义参数
python targon_register.py

# 快速模式 - 预设20个账户，80并发
python quick_start.py
```

### 方法3: 命令行快速启动
```bash
# 极速模式 - 50个账户，100并发
python -c "from targon_register import TargonRegister; TargonRegister(100).run(50)"

# 疯狂模式 - 100个账户，150并发  
python -c "from targon_register import TargonRegister; TargonRegister(150).run(100)"
```

## 📋 运行模式

| 模式 | 账户数 | 并发数 | 预计耗时 | 适用场景 |
|------|--------|--------|----------|----------|
| 交互模式 | 自定义 | 自定义 | 自定义 | 精确控制 |
| 快速模式 | 20 | 80 | ~10秒 | 小批量测试 |
| 极速模式 | 50 | 100 | ~20秒 | 中等批量 |
| 疯狂模式 | 100 | 150 | ~30秒 | 大批量生产 |

## 📤 输出文件

### 1. targon_accounts.txt
完整账户信息，格式：
```
邮箱----密码----cookie----apikey
<EMAIL>----!Qwedcxzas1232----auth_session=6khil7fgj4xhcw3i----sn4_t2as9wyb7fcngwcuyj3rx0g64kdv
<EMAIL>----!Bwefvxzas5678----auth_session=8mjkn9hgl6yhew5k----tn6_x4bs1xzc9gdohxdvzk5sy2h86mew
```

### 2. targon_apikeys.txt  
仅API密钥，格式：
```
sn4_t2as9wyb7fcngwcuyj3rx0g64kdv
tn6_x4bs1xzc9gdohxdvzk5sy2h86mew
```

## ⚙️ 配置参数

### 并发线程数建议
- **测试环境**: 20-50线程
- **生产环境**: 50-100线程  
- **极限模式**: 100-200线程

### 性能优化
- 使用会话池复用连接
- 智能重试机制
- 异步邮箱验证
- 批量结果处理

## 📊 性能指标

在标准配置下的性能表现：

| 并发数 | 成功率 | 平均速度 | 内存占用 |
|--------|--------|----------|----------|
| 50 | 95%+ | 2.5个/秒 | ~100MB |
| 100 | 90%+ | 4.0个/秒 | ~200MB |
| 150 | 85%+ | 5.5个/秒 | ~300MB |

## ⚠️ 注意事项

1. **网络要求**: 需要稳定的网络连接
2. **邮箱API**: 依赖millx.bio邮箱服务
3. **并发限制**: 过高并发可能被服务器限制
4. **成功率**: 网络状况影响最终成功率
5. **合规使用**: 请遵守相关服务条款

## 🔧 故障排除

### 常见问题

**Q: 注册失败率高？**
A: 降低并发数，检查网络连接

**Q: 邮箱验证超时？**  
A: 邮箱服务可能延迟，程序会自动重试

**Q: API密钥获取失败？**
A: 账户激活可能失败，检查验证流程

**Q: 程序卡住不动？**
A: 可能网络问题，Ctrl+C中断后重试

## 🎉 使用示例

```bash
# 启动注册机
python targon_register.py

# 输入参数
请输入要注册的账户数量: 50
请输入并发线程数 (建议50-100): 80

# 确认开始
📋 配置信息:
   注册数量: 50  
   并发线程: 80
   预计耗时: 19秒

确认开始注册? (y/N): y

# 运行过程
🚀 开始批量注册 50 个账户，并发数: 80
✅ 注册成功: <EMAIL>
✅ 获取验证token: <EMAIL>
✅ 账户激活成功，获取到session
✅ 获取API密钥成功: sn4_t2as9wyb7fcngwcuyj3rx0g64kdv
🎉 账户 #1 处理完成

# 完成统计  
📊 注册完成统计:
   总数量: 50
   成功: 47
   失败: 3
   成功率: 94.0%
   耗时: 18.5秒
   平均速度: 2.54个/秒
```

## 📞 技术支持

如有问题请检查：
1. Python环境是否正确安装
2. 依赖库是否完整
3. 网络连接是否稳定
4. 邮箱API是否可用

---

**⚡ 高速注册，追求极致！不在乎服务器死活！**
