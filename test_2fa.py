#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试2FA功能的简单脚本
"""

from totp_utils import generate_totp_code, verify_totp_code
import time

def test_2fa_functionality():
    """测试2FA功能"""
    print("🧪 测试2FA功能...")
    
    # 测试用的secret（类似Targon返回的格式）
    test_secrets = [
        "EASHL5G4RI2XX4K4TRNHALHSCFC75QBO",  # 来自您提供的示例
        "HNRGN4ML2Y472KQPIEACGGGGS3AC6H2Y",  # 来自您提供的示例
        "JBSWY3DPEHPK3PXP"  # 标准测试secret
    ]
    
    for i, secret in enumerate(test_secrets, 1):
        print(f"\n📝 测试Secret #{i}: {secret}")
        
        try:
            # 生成验证码
            code = generate_totp_code(secret)
            print(f"✅ 生成的验证码: {code}")
            
            # 验证验证码
            is_valid = verify_totp_code(secret, code)
            print(f"✅ 验证结果: {'通过' if is_valid else '失败'}")
            
            # 测试时间窗口
            time.sleep(1)
            code2 = generate_totp_code(secret)
            print(f"✅ 1秒后的验证码: {code2}")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    print("\n🎯 2FA功能测试完成！")

def simulate_targon_2fa_flow():
    """模拟Targon 2FA流程"""
    print("\n🚀 模拟Targon 2FA流程...")
    
    # 模拟从API获取的数据
    mock_response = {
        "uri": "otpauth://totp/https%3A%2F%2Ftargon.com:<EMAIL>?secret=EASHL5G4RI2XX4K4TRNHALHSCFC75QBO&issuer=https%3A%2F%2Ftargon.com",
        "twoFactorSecret": "202475f4dc8a357bf15c9c5a702cf21145fec02e",
        "manualCode": "EASHL5G4RI2XX4K4TRNHALHSCFC75QBO"
    }
    
    print(f"📥 模拟API响应:")
    print(f"   URI: {mock_response['uri']}")
    print(f"   Secret: {mock_response['twoFactorSecret']}")
    print(f"   Manual Code: {mock_response['manualCode']}")
    
    # 使用manual code生成验证码
    manual_code = mock_response['manualCode']
    otp_code = generate_totp_code(manual_code)
    
    print(f"\n🔐 生成的OTP验证码: {otp_code}")
    print(f"📤 模拟发送到enable2FA API...")
    
    # 模拟API请求数据
    enable_data = {
        "0": {
            "json": {
                "otp": otp_code,
                "twoFactorSecret": mock_response['twoFactorSecret']
            }
        }
    }
    
    print(f"✅ 模拟请求数据: {enable_data}")
    print("🎉 2FA流程模拟完成！")

if __name__ == "__main__":
    test_2fa_functionality()
    simulate_targon_2fa_flow()
