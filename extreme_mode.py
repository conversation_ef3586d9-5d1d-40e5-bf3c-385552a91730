#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Targon 极限模式 - 超高并发注册
"""

from targon_register import TargonRegister

def extreme_register():
    """极限模式注册"""
    print("💥 Targon 极限模式")
    print("=" * 60)
    print("⚡ 超高并发，追求极致速度！")
    print("🔥 不在乎服务器死活！")
    print("💾 实时保存，数据不丢失！")
    print("=" * 60)
    
    try:
        count = int(input("账户数量: "))
        threads = int(input("并发线程数 (建议500+): "))
        
        if count <= 0 or threads <= 0:
            print("❌ 参数必须大于0")
            return
        
        if threads >= 1000:
            print(f"🚀 超级疯狂模式: {threads} 线程！")
            confirm = input("确认使用超级疯狂模式? (y/N): ")
            if confirm.lower() != 'y':
                return
        elif threads >= 500:
            print(f"⚡ 极限模式: {threads} 线程！")
        
        print(f"\n💥 开始极限注册 {count} 个账户，{threads} 个并发线程")
        print("🔥 全速前进，不留余地！")
        
        register = TargonRegister(max_workers=threads)
        register.run(count)
        
    except ValueError:
        print("❌ 请输入有效数字")
    except KeyboardInterrupt:
        print("\n❌ 用户中断")
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    extreme_register()
