#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看现有数据脚本
"""

import os

def view_existing_data():
    """查看现有的账户和API密钥数据"""
    print("📊 Targon 数据查看器")
    print("=" * 50)
    
    # 检查账户文件
    if os.path.exists('targon_accounts.txt'):
        with open('targon_accounts.txt', 'r', encoding='utf-8') as f:
            accounts = [line.strip() for line in f if line.strip()]
        
        print(f"📄 账户文件: targon_accounts.txt")
        print(f"   总数量: {len(accounts)} 条")
        
        if accounts:
            print("   最近5条:")
            for i, account in enumerate(accounts[-5:], 1):
                parts = account.split('----')
                if len(parts) >= 4:
                    email = parts[0]
                    api_key = parts[3][:20] + "..." if len(parts[3]) > 20 else parts[3]
                    print(f"   {len(accounts)-5+i:2d}. {email} -> {api_key}")
                else:
                    print(f"   {len(accounts)-5+i:2d}. {account}")
    else:
        print("📄 账户文件: 不存在")
    
    print()
    
    # 检查API密钥文件
    if os.path.exists('targon_apikeys.txt'):
        with open('targon_apikeys.txt', 'r', encoding='utf-8') as f:
            api_keys = [line.strip() for line in f if line.strip()]
        
        print(f"🔑 API密钥文件: targon_apikeys.txt")
        print(f"   总数量: {len(api_keys)} 条")
        
        if api_keys:
            print("   最近5条:")
            for i, key in enumerate(api_keys[-5:], 1):
                display_key = key[:20] + "..." if len(key) > 20 else key
                print(f"   {len(api_keys)-5+i:2d}. {display_key}")
    else:
        print("🔑 API密钥文件: 不存在")
    
    print("=" * 50)

def clear_data():
    """清空现有数据"""
    print("\n⚠️  清空数据操作")
    print("这将删除所有现有的账户和API密钥数据！")
    
    confirm1 = input("确认要清空数据吗? (yes/no): ").lower()
    if confirm1 != 'yes':
        print("❌ 操作已取消")
        return
    
    confirm2 = input("再次确认，这是不可逆操作! (YES/no): ")
    if confirm2 != 'YES':
        print("❌ 操作已取消")
        return
    
    try:
        if os.path.exists('targon_accounts.txt'):
            os.remove('targon_accounts.txt')
            print("✅ 已删除 targon_accounts.txt")
        
        if os.path.exists('targon_apikeys.txt'):
            os.remove('targon_apikeys.txt')
            print("✅ 已删除 targon_apikeys.txt")
        
        print("🗑️  所有数据已清空")
    except Exception as e:
        print(f"❌ 清空数据失败: {e}")

def main():
    """主函数"""
    while True:
        view_existing_data()
        
        print("\n操作选项:")
        print("1. 刷新数据")
        print("2. 清空所有数据")
        print("0. 退出")
        
        choice = input("\n选择操作 (0-2): ").strip()
        
        if choice == "0":
            print("👋 再见！")
            break
        elif choice == "1":
            continue  # 重新显示数据
        elif choice == "2":
            clear_data()
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    main()
